import { OpenAI } from "openai";

if (!process.env.OPENAI_API_KEY) {
  throw new Error("Missing OPENAI_API_KEY environment variable");
}

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
}) as OpenAI & { responses: import("openai/resources").Responses };

type OpenAIResponse = {
  output_text?: string;
};

async function callOpenAI(
  fileData: string,
  prompt: string
): Promise<OpenAIResponse> {
  try {
    return await openai.responses.create({
      model: "gpt-4.1-mini",
      input: [
        {
          role: "user",
          content: [
            {
              type: "input_file",
              filename: "invoice.pdf",
              file_data: fileData,
            },
            {
              type: "input_text",
              text: prompt,
            },
          ],
        },
      ],
    });
  } catch (error) {
    console.error("🤖 OpenAI API error:", error);
    return { output_text: undefined };
  }
}

const systemPrompt_InvoiceIssuer = `
Extract the company/person who issued this invoice. Return only the name, nothing else.
`;

export async function extractInvoiceIssuer(fileData: string): Promise<string> {
  const response = await callOpenAI(fileData, systemPrompt_InvoiceIssuer);
  return response.output_text ?? "Unknown Issuer";
}

const systemPrompt_InvoiceDate = `
Extract the invoice date. Return only the date in ISO format (YYYY-MM-DD). If no date is found, return null.
`;

export async function extractInvoiceDate(
  fileData: string
): Promise<string | null> {
  const response = await callOpenAI(fileData, systemPrompt_InvoiceDate);

  const date = response.output_text?.trim();

  // Handle undefined case first
  if (!date) return null;

  // Validate if the response is a valid date or "null"
  if (date === "null") return null;

  // Simple ISO date validation
  if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
    return date;
  }

  return null;
}

const systemPrompt_InvoiceItems = `
Extract the items from this invoice. Return each item on a new line, without any numbering or special characters. 
Only return the actual items, nothing else. If no items are found, return simply "Services". 

Examples of expected output format:

Интернет услуги
TV услуги

or

Sales Navigator Core

or

Chat subscription

or

1 extra fast premium request (o3-mini) beyond 500/month
470 extra fast premium requests beyond 500/month
21 premium tool calls
3 claude-3.7-sonnet-thinking-max requests

or

Google Cloud - Fee for March 2025
`;

export async function extractInvoiceItems(fileData: string): Promise<string[]> {
  const response = await callOpenAI(fileData, systemPrompt_InvoiceItems);

  const content = response.output_text;

  if (!content) return [];

  // Split by newlines and filter out empty lines
  const items = content
    .split("\n")
    .map((item: string) => item.trim())
    .filter((item: string) => item.length > 0);

  return items;
}

const systemPrompt_InvoiceTotal = `
You are a helpful assistant that extracts the total amount and currency from invoice text.

Return the amount as a number and currency as a string, separated by a comma.

Examples of expected output:

- "120.50,USD"
- "19.99,EUR"
- "10.00,BGN"
`;

export async function extractInvoiceTotal(
  fileData: string
): Promise<{ amount: number | null; currency: string | null }> {
  const response = await callOpenAI(fileData, systemPrompt_InvoiceTotal);

  const content = response.output_text;

  if (!content) return { amount: null, currency: null };

  const [amountStr, currency] = content.split(",").map((s: string) => s.trim());

  if (amountStr === "null" || !currency) {
    return { amount: null, currency: null };
  }

  const amount = parseFloat(amountStr);
  if (isNaN(amount)) {
    return { amount: null, currency: null };
  }

  return { amount, currency };
}
