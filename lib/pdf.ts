import type { Buffer } from 'node:buffer';
import PDFParser from 'pdf2json';

export async function extractTextFromPdf(buffer: Buffer): Promise<string> {
  return new Promise((resolve, reject) => {
    const pdfParser = new PDFParser(null, true);

    const originalWarn = console.warn;
    console.warn = (msg) => {
      if (!msg.includes('Unsupported: field.type') && !msg.includes('NOT valid form')) {
        originalWarn(msg);
      }
    };

    pdfParser.on('pdfParser_dataReady', () => {
      try {
        console.warn = originalWarn;

        const text = pdfParser.getRawTextContent()
          .replace(/\\/g, '')
          .replace(/\s+/g, ' ')
          .replace(/\(cid:\d+\)/g, '')
          .replace(/[^\x20-\x7E\n]/g, '')
          .trim();
        
        resolve(text);
      } catch (error) {
        console.warn = originalWarn;
        reject(new Error('Failed to process PDF text'));
      }
    });

    pdfParser.on('pdfParser_dataError', error => {
      console.warn = originalWarn;
      reject(error);
    });

    try {
      pdfParser.parseBuffer(buffer);
    } catch (error) {
      console.warn = originalWarn;
      reject(new Error('Failed to parse PDF buffer'));
    }
  });
} 
