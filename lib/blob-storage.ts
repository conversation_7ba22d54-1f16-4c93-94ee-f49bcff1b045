import { put, del, list } from '@vercel/blob';
import { format } from 'date-fns';
import { Invoice, Transaction } from '@/types/invoice';

interface DbSchema {
  invoices?: Invoice[];
  transactions?: Transaction[];
  transactionToInvoice?: Record<string, string>; // transaction id -> invoice id mapping
  revolutStatementUrl?: string | null; // Vercel Blob Storage URL for the statement PDF
}

// Helper function to create a deep copy of the default database structure
const createDefaultDb = (): DbSchema => ({
  invoices: [],
  transactions: [],
  transactionToInvoice: {},
  revolutStatementUrl: null,
});

// In-memory cache for database data by month
const dbCache = new Map<string, {
  data: DbSchema;
  lastModified: number;
  isDirty: boolean;
}>();

// Debounced write operations to avoid too frequent blob updates
const pendingWrites = new Map<string, NodeJS.Timeout>();
const WRITE_DEBOUNCE_MS = 2000; // 2 seconds

// Helper function to get the blob key for a given month
const getDbKey = (date: Date = new Date()) => {
  const monthYear = format(date, 'yyyy-MM');
  return `db/invoices-${monthYear}.json`;
};

// Helper function to get invoice blob key
const getInvoiceKey = (invoiceId: string, month: string) => {
  return `invoices/${month}/${invoiceId}.pdf`;
};

// Helper function to get statement blob key
const getStatementKey = (month: string) => {
  return `statements/${month}/revolut-statement.pdf`;
};

// Debounced write function to sync cache to blob storage
async function debouncedWrite(key: string, data: DbSchema) {
  // Clear any existing pending write for this key
  const existingTimeout = pendingWrites.get(key);
  if (existingTimeout) {
    clearTimeout(existingTimeout);
  }

  // Set up new debounced write
  const timeout = setTimeout(async () => {
    try {
      // Delete existing blob if it exists
      const { blobs } = await list({ prefix: key });
      if (blobs.length > 0) {
        await del(blobs[0].url);
      }

      // Write new data
      await put(key, JSON.stringify(data), {
        access: 'public',
        contentType: 'application/json',
      });

      // Mark cache as clean
      const cacheEntry = dbCache.get(key);
      if (cacheEntry) {
        cacheEntry.isDirty = false;
      }

      console.log(`✅ Synced database to blob storage: ${key}`);
    } catch (error) {
      console.error(`❌ Failed to sync database to blob storage: ${key}`, error);
    } finally {
      pendingWrites.delete(key);
    }
  }, WRITE_DEBOUNCE_MS);

  pendingWrites.set(key, timeout);
}

// Get database data for a specific month with in-memory caching
async function getDb(date: Date = new Date()) {
  const key = getDbKey(date);

  // Check if we have cached data
  const cached = dbCache.get(key);
  if (cached) {
    return {
      data: cached.data,
      write: async () => {
        const cacheEntry = dbCache.get(key);
        if (cacheEntry) {
          cacheEntry.isDirty = true;
          cacheEntry.lastModified = Date.now();
          await debouncedWrite(key, cacheEntry.data);
        }
      }
    };
  }

  try {
    // Try to list and find existing data blob
    const { blobs } = await list({ prefix: key });

    let data: DbSchema;
    if (blobs.length > 0) {
      // Fetch the blob content
      const response = await fetch(blobs[0].url);
      if (response.ok) {
        data = await response.json();
      } else {
        data = createDefaultDb();
      }
    } else {
      data = createDefaultDb();
    }

    // Ensure all fields exist with defaults
    const safeData = {
      ...createDefaultDb(),
      ...data,
    };

    // Cache the data
    dbCache.set(key, {
      data: safeData,
      lastModified: Date.now(),
      isDirty: false,
    });

    return {
      data: safeData,
      write: async () => {
        const cacheEntry = dbCache.get(key);
        if (cacheEntry) {
          cacheEntry.isDirty = true;
          cacheEntry.lastModified = Date.now();
          // Use the current cached data, not the original safeData
          await debouncedWrite(key, cacheEntry.data);
        } else {
          // Fallback to safeData if cache entry is somehow missing
          await debouncedWrite(key, safeData);
        }
      }
    };
  } catch (error) {
    console.error('Error fetching database:', error);

    // Return default data with write function
    const safeData = createDefaultDb();

    // Cache the default data
    dbCache.set(key, {
      data: safeData,
      lastModified: Date.now(),
      isDirty: false,
    });

    return {
      data: safeData,
      write: async () => {
        const cacheEntry = dbCache.get(key);
        if (cacheEntry) {
          cacheEntry.isDirty = true;
          cacheEntry.lastModified = Date.now();
          await debouncedWrite(key, cacheEntry.data);
        } else {
          // Fallback to safeData if cache entry is somehow missing
          await debouncedWrite(key, safeData);
        }
      }
    };
  }
}

// Store an invoice PDF file
async function storeInvoicePdf(invoiceId: string, month: string, pdfBuffer: Buffer): Promise<string> {
  const key = getInvoiceKey(invoiceId, month);
  
  const blob = await put(key, pdfBuffer, {
    access: 'public',
    contentType: 'application/pdf',
    allowOverwrite: true,
  });
  
  return blob.url;
}

// Get an invoice PDF file
async function getInvoicePdf(invoiceId: string, month: string): Promise<Buffer | null> {
  const key = getInvoiceKey(invoiceId, month);

  try {
    // List blobs to find the invoice
    const { blobs } = await list({ prefix: key });

    if (blobs.length === 0) {
      return null;
    }

    // Fetch the blob content
    const response = await fetch(blobs[0].url);

    if (!response.ok) {
      return null;
    }

    const arrayBuffer = await response.arrayBuffer();
    return Buffer.from(arrayBuffer);
  } catch (error) {
    console.error('Error fetching invoice PDF:', error);
    return null;
  }
}

// Delete an invoice PDF file
async function deleteInvoicePdf(invoiceId: string, month: string): Promise<void> {
  const key = getInvoiceKey(invoiceId, month);

  try {
    // List blobs to find the invoice
    const { blobs } = await list({ prefix: key });

    if (blobs.length > 0) {
      await del(blobs[0].url);
    }
  } catch (error) {
    console.error('Error deleting invoice PDF:', error);
    // Don't throw - deletion might fail if file doesn't exist
  }
}

// Store a statement PDF file
async function storeStatementPdf(month: string, pdfBuffer: Buffer): Promise<string> {
  const key = getStatementKey(month);
  
  const blob = await put(key, pdfBuffer, {
    access: 'public',
    contentType: 'application/pdf',
    allowOverwrite: true,
  });
  
  return blob.url;
}

// Get a statement PDF file
async function getStatementPdf(month: string): Promise<Buffer | null> {
  const key = getStatementKey(month);

  try {
    // List blobs to find the statement
    const { blobs } = await list({ prefix: key });

    if (blobs.length === 0) {
      return null;
    }

    // Fetch the blob content
    const response = await fetch(blobs[0].url);

    if (!response.ok) {
      return null;
    }

    const arrayBuffer = await response.arrayBuffer();
    return Buffer.from(arrayBuffer);
  } catch (error) {
    console.error('Error fetching statement PDF:', error);
    return null;
  }
}

// Check if a statement exists for a given month
async function hasStatement(month: string): Promise<boolean> {
  const key = getStatementKey(month);

  try {
    const { blobs } = await list({ prefix: key });
    return blobs.length > 0;
  } catch (error) {
    return false;
  }
}

// Store a CSV file in blob storage
async function storeCsvFile(filename: string, month: string, csvBuffer: Buffer): Promise<string> {
  const key = `csv/${month}/${filename}`;

  const blob = await put(key, csvBuffer, {
    access: 'public',
    contentType: 'text/csv',
    allowOverwrite: true,
  });

  return blob.url;
}

// Get a CSV file from blob storage
async function getCsvFile(filename: string, month: string): Promise<Buffer | null> {
  const key = `csv/${month}/${filename}`;

  try {
    const { blobs } = await list({ prefix: key });

    if (blobs.length === 0) {
      return null;
    }

    const response = await fetch(blobs[0].url);

    if (!response.ok) {
      return null;
    }

    const arrayBuffer = await response.arrayBuffer();
    return Buffer.from(arrayBuffer);
  } catch (error) {
    console.error('Error fetching CSV file:', error);
    return null;
  }
}

// Cache management utilities
function clearCache() {
  dbCache.clear();
  // Clear all pending writes
  pendingWrites.forEach((timeout) => {
    clearTimeout(timeout);
  });
  pendingWrites.clear();
}

function getCacheStats() {
  const stats = {
    totalCachedMonths: dbCache.size,
    pendingWrites: pendingWrites.size,
    cacheEntries: Array.from(dbCache.entries()).map(([key, entry]) => ({
      key,
      lastModified: new Date(entry.lastModified),
      isDirty: entry.isDirty,
      invoiceCount: entry.data.invoices?.length || 0,
      transactionCount: entry.data.transactions?.length || 0,
    }))
  };
  return stats;
}

// Force immediate sync of all dirty cache entries
async function forceSyncAll() {
  const promises: Promise<void>[] = [];

  dbCache.forEach((entry, key) => {
    if (entry.isDirty) {
      // Cancel debounced write and do immediate write
      const existingTimeout = pendingWrites.get(key);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
        pendingWrites.delete(key);
      }

      promises.push(debouncedWrite(key, entry.data));
    }
  });

  await Promise.all(promises);
}

export {
  getDb,
  getDbKey,
  storeInvoicePdf,
  getInvoicePdf,
  deleteInvoicePdf,
  storeStatementPdf,
  getStatementPdf,
  hasStatement,
  storeCsvFile,
  getCsvFile,
  clearCache,
  getCacheStats,
  forceSyncAll
};
export type { Invoice, DbSchema };
