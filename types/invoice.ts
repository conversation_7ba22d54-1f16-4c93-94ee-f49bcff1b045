export interface Invoice {
  id: string;
  name: string;
  pdfUrl: string; // Vercel Blob Storage URL for the PDF
  timestamp: number;
  issuer: string;
  hasMyVAT: boolean;
  extractedText: string;
  issueDate: string | null;
  items: string[];
  totalAmount: number | null;
  currency: string | null;
}

export interface Transaction {
  dateStarted: string | null;
  dateCompleted: string | null;
  id: string;
  type: string;
  state: string;
  description: string;
  reference: string;
  payer: string;
  cardNumber: string;
  cardLabel: string;
  cardState: string;
  origCurrency: string;
  origAmount: number | null;
  paymentCurrency: string;
  amount: number | null;
  totalAmount: number | null;
  exchangeRate: number | null;
  fee: number | null;
  feeCurrency: string;
  balance: number | null;
  account: string;
  beneficiaryAccountNumber: string;
  beneficiarySortCode: string;
  beneficiaryIBAN: string;
  beneficiaryBIC: string;
  mcc: string;
  relatedTransactionId: string;
  spendProgram: string;
} 
