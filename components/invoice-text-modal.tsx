import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface InvoiceTextModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice?: {
    name: string;
    extractedText: string;
  };
}

export function InvoiceTextModal({ isOpen, onClose, invoice }: InvoiceTextModalProps) {
  if (!invoice) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>{invoice.name}</DialogTitle>
        </DialogHeader>
        <div className="overflow-y-auto">
          <pre className="whitespace-pre-wrap bg-muted p-4 rounded-lg text-sm">
            {invoice.extractedText}
          </pre>
        </div>
      </DialogContent>
    </Dialog>
  );
} 
