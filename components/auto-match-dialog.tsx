import { Loader2 } from "lucide-react";
import { useState } from "react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { type Invoice, type Transaction } from "@/types/invoice";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onAccept: (matches: Array<{ transactionId: string; invoiceId: string }>) => Promise<void>;
  transactions: Transaction[];
  invoices: Invoice[];
  transactionToInvoice: Record<string, string>;
};

const normalizeText = (text: string) => {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, " ")
    .split(/\s+/)
    .filter(Boolean);
};

const findMatches = (
  transactions: Transaction[],
  invoices: Invoice[],
  transactionToInvoice: Record<string, string>
) => {
  const matches: Array<{ transaction: Transaction; invoice: Invoice }> = [];

  // Get unassigned transactions and invoices
  const unassignedTransactions = transactions.filter(
    (t) => !transactionToInvoice[t.id]
  );
  const unassignedInvoices = invoices.filter(
    (i) => !Object.values(transactionToInvoice).includes(i.id)
  );

  for (const transaction of unassignedTransactions) {
    for (const invoice of unassignedInvoices) {
      // Check amount match (ignoring sign)
      if (
        transaction.amount !== null &&
        invoice.totalAmount !== null &&
        Math.abs(transaction.amount) === Math.abs(invoice.totalAmount)
      ) {
        // Check text match
        const transactionWords = normalizeText(transaction.description || "");
        const invoiceWords = normalizeText(invoice.issuer || "");

        const hasWordMatch = transactionWords.some((word) =>
          invoiceWords.includes(word)
        );

        if (hasWordMatch) {
          matches.push({ transaction, invoice });
          break; // Move to next transaction once we find a match
        } else {
          // Log potential matches that failed due to word matching
          console.log("🔍 Potential match found but no word overlap:", {
            transaction: {
              description: transaction.description,
              words: transactionWords,
              amount: transaction.amount,
              currency: transaction.paymentCurrency,
            },
            invoice: {
              issuer: invoice.issuer,
              words: invoiceWords,
              amount: invoice.totalAmount,
              currency: invoice.currency,
            },
          });
        }
      }
    }
  }

  return matches;
};

export function AutoMatchDialog({
  isOpen,
  onClose,
  onAccept,
  transactions,
  invoices,
  transactionToInvoice,
}: Props) {
  const [isProcessing, setIsProcessing] = useState(false);
  const matches = findMatches(transactions, invoices, transactionToInvoice);

  const handleAccept = async () => {
    setIsProcessing(true);
    try {
      await onAccept(
        matches.map((m) => ({
          transactionId: m.transaction.id,
          invoiceId: m.invoice.id,
        }))
      );
      onClose();
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="max-w-2xl">
        <AlertDialogHeader>
          <AlertDialogTitle>Auto-match Transactions</AlertDialogTitle>
          <AlertDialogDescription>
            Found {matches.length} potential matches between transactions and invoices.
            Review the matches below before accepting.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="max-h-[60vh] overflow-y-auto space-y-2 my-4">
          {matches.map(({ transaction, invoice }) => (
            <div
              key={`${transaction.id}-${invoice.id}`}
              className="bg-muted/50 rounded-lg p-2 flex items-center gap-4"
            >
              <div className="flex-1 truncate">
                {transaction.description}
              </div>
              <div className="flex-1 truncate text-right">
                {invoice.issuer}
              </div>
              <div className="w-24 text-right font-mono">
                {Math.abs(transaction.amount || 0).toFixed(2)}
              </div>
              <div className="w-16 text-right">
                {transaction.paymentCurrency}
              </div>
            </div>
          ))}
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isProcessing}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleAccept}
            disabled={isProcessing || matches.length === 0}
            className="bg-primary text-primary-foreground hover:bg-primary/90"
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              `Accept ${matches.length} Matches`
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
} 