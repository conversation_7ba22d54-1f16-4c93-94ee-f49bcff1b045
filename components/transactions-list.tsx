import { format } from "date-fns";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Loader2, Trash2, Upload, Wand2 } from "lucide-react";
import { Fragment, useState } from "react";

import { AutoMatchDialog } from "@/components/auto-match-dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import { type Invoice, type Transaction } from "@/types/invoice";
import { getInvoiceHelperLinks } from "@/utils/get-invoice-helper-links";

type Props = {
  transactions: Transaction[];
  isLoading: boolean;
  isUploading: boolean;
  onTransactionClick: (transaction: Transaction) => void;
  onClearTransactions: () => Promise<void>;
  transactionToInvoice: Record<string, string>;
  invoices: Invoice[];
  onAutoMatch: (matches: Array<{ transactionId: string; invoiceId: string }>) => Promise<void>;
  onIgnoreTransaction: (transactionId: string) => Promise<void>;
};

const formatSafeDate = (dateString: string | null) => {
  if (!dateString) return <span className="text-muted-foreground">-</span>;
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      throw new Error("Invalid date");
    }
    return format(date, "MMM d, yyyy");
  } catch (error) {
    return <span className="text-muted-foreground">Invalid date</span>;
  }
};

const formatSafeNumber = (num: number | null) => {
  if (num === null || num === undefined || isNaN(num)) {
    return <span className="text-muted-foreground">-</span>;
  }
  return num.toFixed(2);
};

export function TransactionsList({
  transactions,
  isLoading,
  isUploading,
  onTransactionClick,
  onClearTransactions,
  transactionToInvoice,
  invoices,
  onAutoMatch,
  onIgnoreTransaction,
}: Props) {
  const [isAutoMatchOpen, setIsAutoMatchOpen] = useState(false);
  const [isAutoMatching, setIsAutoMatching] = useState(false);
  const [ignoringTransactionIds, setIgnoringTransactionIds] = useState<Set<string>>(new Set());

  const handleAutoMatch = async (matches: Array<{ transactionId: string; invoiceId: string }>) => {
    setIsAutoMatching(true);
    try {
      await onAutoMatch(matches);
    } finally {
      setIsAutoMatching(false);
    }
  };

  const handleIgnoreClick = async (transactionId: string) => {
    setIgnoringTransactionIds((prev) => new Set(prev).add(transactionId));
    try {
      await onIgnoreTransaction(transactionId);
    } finally {
      setIgnoringTransactionIds((prev) => {
        const next = new Set(prev);
        next.delete(transactionId);
        return next;
      });
    }
  };

  if (isLoading) {
    return (
      <div className="text-center py-12 text-muted-foreground">
        <Loader2 className="mx-auto h-12 w-12 mb-4 animate-spin" />
        <p className="text-lg font-medium mb-2">Loading transactions...</p>
      </div>
    );
  }

  if (transactions.length === 0 && !isUploading) {
    return (
      <div className="text-center py-12 text-muted-foreground">
        <Upload className="mx-auto h-12 w-12 mb-4" />
        <p className="text-lg font-medium mb-2">Drop Transactions CSV Here</p>
        <p className="text-sm mb-4">Drag and drop your transactions CSV file to upload</p>
        <a
          href="https://business.revolut.com/overview?popupType=closed&type=statements&id=bf64bf1d-d357-4bc1-9f8f-1e0dc9b9b5ff&accountType=all-main"
          target="_blank"
          rel="noopener noreferrer"
          className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
        >
          Download from Revolut Business →
        </a>
      </div>
    );
  }

  // Calculate totals by currency for visible transactions
  const totals: Record<string, number> = {};
  transactions.forEach((t: Transaction) => {
    if (t.amount !== null && t.paymentCurrency) {
      totals[t.paymentCurrency] = (totals[t.paymentCurrency] || 0) + t.amount;
    }
  });

  return (
    <div className="overflow-x-auto">
      <Table className="text-sm border rounded shadow-sm">
        <TableBody>
          {transactions.map((transaction: Transaction, index: number) => {
            const linkedInvoiceId = transactionToInvoice[transaction.id];
            const linkedInvoice = linkedInvoiceId
              ? invoices.find((inv) => inv.id === linkedInvoiceId)
              : null;
            const isIgnoringThisTransaction = ignoringTransactionIds.has(transaction.id);

            return (
              <Fragment key={`${transaction.id}-${index}`}>
                <TableRow
                  className="h-8 hover:bg-muted/40 cursor-pointer"
                  onClick={() => onTransactionClick(transaction)}
                >
                  <TableCell className="px-2 py-1 w-8" />
                  <TableCell className="px-2 py-1 whitespace-nowrap">
                    {formatSafeDate(transaction.dateCompleted)}
                  </TableCell>
                  <TableCell className="px-2 py-1 truncate max-w-xs">
                    {transaction.description || "-"}
                  </TableCell>
                  <TableCell
                    className={`px-2 py-1 text-right font-mono ${
                      transaction.amount !== null && transaction.amount < 0
                        ? "text-red-600"
                        : "text-green-600"
                    }`}
                  >
                    {formatSafeNumber(transaction.amount)}
                  </TableCell>
                  <TableCell className="px-2 py-1">
                    {transaction.paymentCurrency || "-"}
                  </TableCell>
                </TableRow>
                {/* Second row for linked invoice or unmapped status */}
                {linkedInvoiceId === "<ignore>" ? (
                  <TableRow className="bg-muted/50 text-xs">
                    <TableCell colSpan={5} className="px-2 py-1 text-muted-foreground text-center">
                      Ignored
                    </TableCell>
                  </TableRow>
                ) : linkedInvoice ? (
                  <TableRow
                    className={
                      linkedInvoice ? "bg-blue-50 text-xs" : "bg-red-50 text-xs"
                    }
                  >
                    <TableCell className="px-2 py-1 w-8 text-center align-middle">
                      {linkedInvoice ? (
                        <CheckCircle className="text-blue-500 inline-block h-4 w-4" />
                      ) : (
                        <AlertTriangle className="text-orange-500 inline-block h-4 w-4" />
                      )}
                    </TableCell>
                    <>
                      <TableCell className="px-2 py-1 whitespace-nowrap">
                        {linkedInvoice.issueDate
                          ? formatSafeDate(linkedInvoice.issueDate)
                          : "-"}
                      </TableCell>
                      <TableCell className="px-2 py-1 truncate max-w-xs">
                        <span className="truncate inline-block max-w-full">
                          {linkedInvoice.name}
                          {linkedInvoice.issuer && (
                            <span className="text-muted-foreground">
                              {" "}
                              ({linkedInvoice.issuer})
                            </span>
                          )}
                        </span>
                      </TableCell>
                      <TableCell className="px-2 py-1 text-right font-mono">
                        {typeof linkedInvoice.totalAmount === "number"
                          ? linkedInvoice.totalAmount.toFixed(2)
                          : "-"}
                      </TableCell>
                      <TableCell className="px-2 py-1">
                        {linkedInvoice.currency || "-"}
                      </TableCell>
                    </>
                  </TableRow>
                ) : (
                  // Only show if not linked and not ignored
                  <TableRow className="bg-red-50 text-xs">
                     <TableCell className="px-2 py-1 w-8 text-center align-middle">
                        <AlertTriangle className="text-orange-500 inline-block h-4 w-4" />
                    </TableCell>
                    <TableCell
                      colSpan={4}
                      className="px-2 py-1 text-xs text-red-700 font-semibold"
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          Invoice not mapped
                          {transaction.description && (
                            <div className="mt-1 space-y-1">
                              {getInvoiceHelperLinks(transaction.description).map((link, i) => (
                                <a
                                  key={i}
                                  href={link}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="block text-blue-600 hover:text-blue-800 hover:underline"
                                >
                                  {link}
                                </a>
                              ))}
                            </div>
                          )}
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-muted-foreground hover:text-foreground"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleIgnoreClick(transaction.id);
                          }}
                          disabled={isIgnoringThisTransaction}
                        >
                          {isIgnoringThisTransaction ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Ignoring...
                            </>
                          ) : (
                            "Ignore"
                          )}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </Fragment>
            );
          })}
        </TableBody>
      </Table>
      {/* Totals footer below the table */}
      <div className="flex justify-between items-center text-xs text-muted-foreground mt-2">
        <div className="flex gap-0">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="text-muted-foreground hover:text-destructive"
                disabled={isLoading || transactions.length === 0}
              >
                <Trash2 className="h-4 w-4 mr-2" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Clear all transactions?</AlertDialogTitle>
                <AlertDialogDescription>
                  This will remove all transactions for this month. This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={onClearTransactions}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  Clear All Transactions
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
          <Button
            variant="ghost"
            size="sm"
            className="text-muted-foreground hover:text-primary"
            onClick={() => setIsAutoMatchOpen(true)}
            disabled={isLoading || transactions.length === 0 || isAutoMatching}
          >
            {isAutoMatching ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Auto-matching...
              </>
            ) : (
              <>
                <Wand2 className="h-4 w-4 mr-2" />
                Auto-match
              </>
            )}
          </Button>
        </div>
        <div className="flex gap-4">
          {Object.entries(totals).map(([currency, total]) => (
            <span key={currency} className="font-mono">
              Total {currency}:{" "}
              <span className={total < 0 ? "text-red-600" : "text-green-600"}>
                {total.toFixed(2)}
              </span>
            </span>
          ))}
        </div>
      </div>

      <AutoMatchDialog
        isOpen={isAutoMatchOpen}
        onClose={() => setIsAutoMatchOpen(false)}
        onAccept={handleAutoMatch}
        transactions={transactions}
        invoices={invoices}
        transactionToInvoice={transactionToInvoice}
      />
    </div>
  );
} 