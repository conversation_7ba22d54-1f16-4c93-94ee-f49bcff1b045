import { useState } from "react";
import { format } from "date-fns";
import { Search } from "lucide-react";
import { type Invoice, type Transaction } from "@/types/invoice";
import { formatCurrency } from "@/utils/format-currency";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface InvoiceSelectDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (invoiceId: string | null) => void;
  invoices: Invoice[];
  selectedInvoiceId?: string;
  transaction?: Transaction | null;
  matchedInvoiceIds?: string[];
}

export function InvoiceSelectDialog({
  isOpen,
  onClose,
  onSelect,
  invoices,
  selectedInvoiceId,
  transaction,
  matchedInvoiceIds = [],
}: InvoiceSelectDialogProps) {
  const [search, setSearch] = useState("");

  const filteredInvoices = invoices.filter((invoice) => {
    const searchLower = search.toLowerCase();
    return (
      invoice.issuer.toLowerCase().includes(searchLower) ||
      invoice.name.toLowerCase().includes(searchLower) ||
      (invoice.items?.some((item) =>
        item.toLowerCase().includes(searchLower)
      ) ?? false)
    );
  });

  // Grouping logic
  let perfectMatch: Invoice[] = [];
  let available: Invoice[] = [];
  let alreadyMatched: Invoice[] = [];

  if (transaction && typeof transaction.amount === 'number') {
    const absTxnAmount = Math.abs(transaction.amount);
    // Find perfect matches (not already matched elsewhere, except the one currently linked)
    perfectMatch = filteredInvoices.filter(inv =>
      typeof inv.totalAmount === 'number' &&
      Math.abs(inv.totalAmount) === absTxnAmount &&
      !matchedInvoiceIds.includes(inv.id)
    );
    // Available: not matched elsewhere, not perfect match
    available = filteredInvoices.filter(inv =>
      !matchedInvoiceIds.includes(inv.id) &&
      !perfectMatch.includes(inv)
    ).sort((a, b) => {
      const diffA = typeof a.totalAmount === 'number' ? Math.abs(Math.abs(a.totalAmount) - absTxnAmount) : Number.MAX_VALUE;
      const diffB = typeof b.totalAmount === 'number' ? Math.abs(Math.abs(b.totalAmount) - absTxnAmount) : Number.MAX_VALUE;
      return diffA - diffB;
    });
    // Already matched: in matchedInvoiceIds (but not the one currently linked)
    alreadyMatched = filteredInvoices.filter(inv => matchedInvoiceIds.includes(inv.id));
  } else {
    // No transaction: just split by matched/unmatched
    available = filteredInvoices.filter(inv => !matchedInvoiceIds.includes(inv.id));
    alreadyMatched = filteredInvoices.filter(inv => matchedInvoiceIds.includes(inv.id));
  }

  const formatAmount = (amount: number | null, currency: string | null) => {
    return formatCurrency(amount, currency) ?? "N/A";
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "-";
    try {
      return format(new Date(dateString), "MMM dd, yyyy");
    } catch {
      return "-";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Select Invoice</DialogTitle>
        </DialogHeader>

        {/* Transaction context */}
        {transaction && (
          <div className="mb-4 p-3 rounded bg-muted/50 border flex flex-col gap-1">
            <div className="flex flex-wrap gap-2 items-center text-sm">
              <span className="font-semibold">For transaction:</span>
              <span className="truncate max-w-xs" title={transaction.description}>{transaction.description || "-"}</span>
              <span className="text-muted-foreground">•</span>
              <span>{formatDate(transaction.dateCompleted)}</span>
              <span className="text-muted-foreground">•</span>
              <span className="tabular-nums font-mono">
                {formatAmount(transaction.amount ?? null, transaction.paymentCurrency ?? null)}
              </span>
            </div>
          </div>
        )}

        <div className="relative mb-2">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by issuer, filename, or items..."
            className="pl-8"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>

        <div className="max-h-[60vh] overflow-y-auto space-y-1">
          {/* Perfect match group */}
          {perfectMatch.length > 0 && (
            <div>
              <div className="text-xs font-semibold text-green-700 mb-1">Perfect match</div>
              {perfectMatch.map((invoice) => (
                <Button
                  key={invoice.id}
                  variant={selectedInvoiceId === invoice.id ? "default" : "outline"}
                  className={
                    `w-full h-auto py-3 px-4 flex flex-col items-stretch text-left rounded-lg border transition-colors ` +
                    `hover:bg-muted/30 focus-visible:ring-2 focus-visible:ring-primary/50` +
                    (selectedInvoiceId === invoice.id ? " ring-2 ring-primary/50" : "")
                  }
                  onClick={() => onSelect(invoice.id)}
                >
                  <div className="flex flex-row items-center gap-4 w-full">
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{invoice.issuer}</div>
                      <div className="text-xs text-muted-foreground truncate">{invoice.name}</div>
                    </div>
                    <div className="flex flex-col items-end min-w-[110px]">
                      <span className="tabular-nums font-mono text-base">
                        {formatAmount(invoice.totalAmount ?? null, invoice.currency ?? null)}
                      </span>
                      <span className="text-xs text-muted-foreground">{formatDate(invoice.issueDate)}</span>
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          )}

          {/* Other available group */}
          {available.length > 0 && (
            <div>
              {perfectMatch.length > 0 && (
                <div className="text-xs font-semibold text-muted-foreground mt-3 mb-1">Other available</div>
              )}
              {available.map((invoice) => (
                <Button
                  key={invoice.id}
                  variant={selectedInvoiceId === invoice.id ? "default" : "outline"}
                  className={
                    `w-full h-auto py-3 px-4 flex flex-col items-stretch text-left rounded-lg border transition-colors ` +
                    `hover:bg-muted/30 focus-visible:ring-2 focus-visible:ring-primary/50` +
                    (selectedInvoiceId === invoice.id ? " ring-2 ring-primary/50" : "")
                  }
                  onClick={() => onSelect(invoice.id)}
                >
                  <div className="flex flex-row items-center gap-4 w-full">
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{invoice.issuer}</div>
                      <div className="text-xs text-muted-foreground truncate">{invoice.name}</div>
                    </div>
                    <div className="flex flex-col items-end min-w-[110px]">
                      <span className="tabular-nums font-mono text-base">
                        {formatAmount(invoice.totalAmount ?? null, invoice.currency ?? null)}
                      </span>
                      <span className="text-xs text-muted-foreground">{formatDate(invoice.issueDate)}</span>
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          )}

          {/* Already matched group */}
          {alreadyMatched.length > 0 && (
            <div>
              <div className="text-xs font-semibold text-muted-foreground mt-3 mb-1">Already matched</div>
              {alreadyMatched.map((invoice) => (
                <Button
                  key={invoice.id}
                  variant="outline"
                  className="w-full h-auto py-3 px-4 flex flex-col items-stretch text-left rounded-lg border bg-muted/40 text-muted-foreground opacity-60 cursor-not-allowed"
                  disabled
                >
                  <div className="flex flex-row items-center gap-4 w-full">
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{invoice.issuer}</div>
                      <div className="text-xs text-muted-foreground truncate">{invoice.name}</div>
                    </div>
                    <div className="flex flex-col items-end min-w-[110px]">
                      <span className="tabular-nums font-mono text-base">
                        {formatAmount(invoice.totalAmount ?? null, invoice.currency ?? null)}
                      </span>
                      <span className="text-xs text-muted-foreground">{formatDate(invoice.issueDate)}</span>
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          )}
        </div>

        <div className="flex justify-end gap-2 mt-4">
          <Button variant="outline" onClick={() => onSelect(null)}>
            Clear Selection
          </Button>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
} 