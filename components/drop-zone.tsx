import { type ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface DropZoneProps {
  children: ReactNode;
  isDragging: boolean;
  onDragOver: (e: React.DragEvent<HTMLDivElement>) => void;
  onDragLeave: (e: React.DragEvent<HTMLDivElement>) => void;
  onDrop: (e: React.DragEvent<HTMLDivElement>) => void;
  className?: string;
}

export function DropZone({
  children,
  isDragging,
  onDragOver,
  onDragLeave,
  onDrop,
  className,
}: DropZoneProps) {
  return (
    <div
      onDragOver={onDragOver}
      onDragLeave={onDragLeave}
      onDrop={onDrop}
      className={cn(
        'transition-colors rounded-lg',
        isDragging && 'ring-2 ring-primary bg-primary/5',
        className
      )}
    >
      {children}
    </div>
  );
} 