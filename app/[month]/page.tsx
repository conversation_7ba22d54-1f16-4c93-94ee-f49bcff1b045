"use client";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import { addMonths, format, parse } from "date-fns";
import
  {
    ChevronLeft,
    ChevronRight,
    Download,
    FileText,
    Loader2,
    RefreshCw,
    Trash2,
    Upload
  } from "lucide-react";
import { useRouter } from "next/navigation";
import { Fragment, useState } from "react";

import { DropZone } from "@/components/drop-zone";
import { InvoiceSelectDialog } from "@/components/invoice-select-dialog";
import { InvoiceTextModal } from "@/components/invoice-text-modal";
import { TransactionsList } from "@/components/transactions-list";
import
  {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
  } from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { type Invoice, type Transaction } from "@/types/invoice";
import { createEmailMessage } from "@/utils/create-email-message";
import { formatCurrency } from "@/utils/format-currency";

async function fetchInvoices(month: string): Promise<Invoice[]> {
  console.log("🔄 Fetching invoices for month:", month);
  const response = await fetch(`/api/invoices?month=${month}`);
  if (!response.ok) {
    throw new Error("Failed to fetch invoices");
  }
  return response.json();
}

const downloadAllInvoicesToFolder = async (
  invoices: Invoice[],
  month: string
) => {
  if (!("showDirectoryPicker" in window)) {
    alert(
      "Your browser does not support the File System Access API. Please use Chrome or Edge."
    );
    return;
  }

  try {
    // Prompt user to pick a directory
    // @ts-ignore
    const dirHandle = await window.showDirectoryPicker();

    for (const invoice of invoices) {
      const response = await fetch(
        `/api/invoices/${invoice.id}?month=${month}`
      );
      if (!response.ok) throw new Error(`Failed to download ${invoice.name}`);
      const blob = await response.blob();

      // Create a file in the selected directory
      const fileHandle = await dirHandle.getFileHandle(invoice.name, {
        create: true,
      });
      const writable = await fileHandle.createWritable();
      await writable.write(blob);
      await writable.close();
      console.log("✅ Saved:", invoice.name);
    }
    alert("All invoices saved to the selected folder!");
  } catch (err) {
    console.error("❌ Error saving invoices:", err);
    alert("Failed to save invoices: " + err);
  }
};

// Remove the old formatAmount function and its currency mappings
const formatAmount = (amount: number | null, currency: string | null) => {
  return formatCurrency(amount, currency) ?? "-";
};

// Helper links configuration
const HELPER_LINKS = [
  {
    keywords: ["linkedin"],
    links: [
      "https://www.linkedin.com/manage/purchases-payments?account=*********",
    ],
  },
  {
    keywords: ["fal.ai", "features labels"],
    links: ["https://fal.ai/dashboard/billing"],
  },
  {
    keywords: ["cursor", "anysphere"],
    links: ["https://www.cursor.com/settings"],
  },
  {
    keywords: ["t3 chat"],
    links: ["https://t3.chat/settings/subscription"],
  },
  {
    keywords: ["vercel"],
    links: ["https://vercel.com/account/settings/invoices"],
  },
  {
    keywords: ["clicky"],
    links: ["https://clicky.com/user/payments"],
  },
  {
    keywords: ["github"],
    links: ["https://github.com/account/billing/history"],
  },
  {
    keywords: ["discord"],
    links: ["https://discord.com/channels/@me"],
  },
  {
    keywords: ["ozone"],
    links: ["https://www.ozone.bg/customerutils/return/invoice"],
  },
  {
    keywords: ["stability"],
    links: ["https://mail.google.com/mail/u/1/#search/stability+invoice"],
  },
  {
    keywords: ["stackblitz"],
    links: ["https://mail.google.com/mail/u/1/#search/stackblitz+invoice"],
  },
  {
    keywords: ["google cloud"],
    links: ["https://mail.google.com/mail/u/1/#search/google+cloud+invoice"],
  },
  {
    keywords: ["google workspace"],
    links: [
      "https://mail.google.com/mail/u/0/#search/google+workspace+invoice",
    ],
  },
  {
    keywords: ["zoho"],
    links: ["https://store.zoho.eu/html/store/mytransaction.html"],
  },
  {
    keywords: ["digital ocean"],
    links: ["https://mail.google.com/mail/u/1/#search/digital+ocean+invoice"],
  },
] as const;

// Helper function to find matching links for a transaction
const findHelperLinks = (description: string) => {
  const desc = description.toLowerCase();
  return HELPER_LINKS.filter(({ keywords }) =>
    keywords.some((keyword) => desc.includes(keyword.toLowerCase()))
  ).flatMap(({ links }) => links);
};

export default function MonthPage({ params }: { params: { month: string } }) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isDragging, setIsDragging] = useState(false);
  const [isDraggingTransactions, setIsDraggingTransactions] = useState(false);
  const [uploadingFiles, setUploadingFiles] = useState<Map<string, boolean>>(
    new Map()
  );
  const [uploadingTransactions, setUploadingTransactions] = useState(false);
  const [deletingFiles, setDeletingFiles] = useState<Set<string>>(new Set());
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | undefined>();
  const [isDeletingAll, setIsDeletingAll] = useState(false);
  const { toast } = useToast();
  const [selectedTransaction, setSelectedTransaction] =
    useState<Transaction | null>(null);
  const [linkingInvoice, setLinkingInvoice] = useState(false);

  // Parse the month from the URL
  const currentMonth = parse(params.month, "yyyy-MM", new Date());

  const { data: invoices = [], isLoading } = useQuery({
    queryKey: ["invoices", params.month],
    queryFn: () => fetchInvoices(params.month),
    staleTime: 1000 * 60 * 5, // Consider data fresh for 5 minutes
  });
  invoices.reverse();

  const {
    data: transactionsData = {
      transactions: [],
      transactionToInvoice: {},
      revolutStatementUrl: null,
    },
    isLoading: isLoadingTransactions,
  } = useQuery({
    queryKey: ["transactions", params.month],
    queryFn: async () => {
      console.log("🔄 Fetching transactions for month:", params.month);
      const response = await fetch(`/api/transactions?month=${params.month}`);
      if (!response.ok) {
        throw new Error("Failed to fetch transactions");
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 5,
  });

  // Allowed transaction types (uncomment to allow more)
  const allowedTransactionTypes = [
    "CARD_PAYMENT",
    // "FEE",
    // "EXCHANGE",
    // "TOPUP",
  ];

  // Filtering controls
  const IGNORE_POSITIVE_AMOUNTS = true; // set to true to hide positive amounts
  const IGNORE_EXCHANGE_ROWS = true; // set to true to hide rows with exchangeRate filled
  const IGNORE_REVOLUT_BUSINESS_FEE = true; // set to true to hide rows with description 'Revolut Business Fee'

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.currentTarget.contains(e.relatedTarget as Node)) {
      return;
    }
    setIsDragging(false);
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    const pdfFiles = files.filter((file) => file.type === "application/pdf");

    if (pdfFiles.length === 0) {
      toast({
        title: "Invalid files",
        description: "Please drop PDF files only",
        variant: "destructive",
      });
      return;
    }

    setUploadingFiles(new Map(pdfFiles.map((f) => [f.name, true])));

    try {
      const formData = new FormData();
      pdfFiles.forEach((file) => {
        formData.append("files", file);
      });

      const response = await fetch("/api/invoices", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) throw new Error("Upload failed");

      const data = await response.json();

      // Create detailed success message grouped by month
      const createSuccessMessage = (results: any[]) => {
        if (results.length === 0) return "No files uploaded";

        // Group results by month
        const byMonth: Record<string, any[]> = {};
        results.forEach(result => {
          // Use monthString if available, otherwise format from issueDate or current date
          const monthKey = result.monthString ?
            format(parse(result.monthString, 'yyyy-MM', new Date()), 'MMMM yyyy') :
            result.issueDate ?
              format(new Date(result.issueDate), 'MMMM yyyy') :
              format(new Date(), 'MMMM yyyy');

          if (!byMonth[monthKey]) {
            byMonth[monthKey] = [];
          }
          byMonth[monthKey].push(result);
        });

        // Create formatted React component
        return (
          <div className="space-y-3">
            {Object.entries(byMonth).map(([month, invoices]) => (
              <div key={month} className="space-y-1">
                <div className="font-medium text-sm">{month}</div>
                <div className="space-y-0.5">
                  {invoices.map((inv, idx) => (
                    <div key={idx} className="text-sm text-muted-foreground ml-2">
                      • Invoice from {inv.issuer || 'Unknown Company'}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        );
      };

      if (data.errors?.length > 0) {
        toast({
          title: "Partial success",
          description: (
            <div className="space-y-2">
              <div>{data.results.length} files uploaded, {data.errors.length} failed</div>
              {data.results.length > 0 && createSuccessMessage(data.results)}
            </div>
          ),
          variant: "default",
        });
      } else {
        toast({
          title: 'Successfully uploaded',
          description: createSuccessMessage(data.results),
        });
      }

      // Invalidate cache for all affected months to trigger refetch
      if (data.affectedMonths && data.affectedMonths.length > 0) {
        console.log("🔄 Invalidating cache for months:", data.affectedMonths);
        await Promise.all(
          data.affectedMonths.map((month: string) =>
            queryClient.invalidateQueries({
              queryKey: ["invoices", month],
            })
          )
        );
      } else {
        // Fallback to current month if no affected months info
        console.log("🔄 Invalidating cache for current month:", params.month);
        await queryClient.invalidateQueries({
          queryKey: ["invoices", params.month],
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to upload files",
        variant: "destructive",
      });
    } finally {
      setUploadingFiles(new Map());
    }
  };

  const handleDelete = async (id: string, name: string) => {
    setDeletingFiles((prev) => new Set(prev).add(id));

    try {
      const response = await fetch(
        `/api/invoices/${id}?month=${params.month}`,
        {
          method: "DELETE",
        }
      );

      if (!response.ok) throw new Error("Delete failed");

      // Invalidate the current month's query to trigger a refetch
      await queryClient.invalidateQueries({
        queryKey: ["invoices", params.month],
      });

      toast({
        title: "Success",
        description: `${name} deleted successfully`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to delete ${name}`,
        variant: "destructive",
      });
    } finally {
      setDeletingFiles((prev) => {
        const next = new Set(prev);
        next.delete(id);
        return next;
      });
    }
  };

  const handleDownload = async (id: string, name: string) => {
    try {
      const response = await fetch(`/api/invoices/${id}?month=${params.month}`);
      if (!response.ok) throw new Error("Download failed");

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = name;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to download ${name}`,
        variant: "destructive",
      });
    }
  };

  const handleDeleteAll = async () => {
    setIsDeletingAll(true);
    try {
      const response = await fetch(`/api/invoices?month=${params.month}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Delete failed");

      // Invalidate the current month's query to trigger a refetch
      await queryClient.invalidateQueries({
        queryKey: ["invoices", params.month],
      });

      toast({
        title: "Success",
        description: "All invoices deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete all invoices",
        variant: "destructive",
      });
    } finally {
      setIsDeletingAll(false);
    }
  };

  const emailText = createEmailMessage(
    invoices,
    Boolean(transactionsData.revolutStatementUrl)
  );

  const handlePrevMonth = () => {
    const prevMonth = addMonths(currentMonth, -1);
    router.push(`/${format(prevMonth, "yyyy-MM")}`);
  };

  const handleNextMonth = () => {
    const nextMonth = addMonths(currentMonth, 1);
    router.push(`/${format(nextMonth, "yyyy-MM")}`);
  };

  const handleTransactionsDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingTransactions(true);
  };

  const handleTransactionsDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.currentTarget.contains(e.relatedTarget as Node)) {
      return;
    }
    setIsDraggingTransactions(false);
  };

  const handleTransactionsDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingTransactions(false);

    const files = Array.from(e.dataTransfer.files);
    const pdfFiles = files.filter((file) => file.type === "application/pdf");
    const csvFiles = files.filter((file) => file.type === "text/csv");

    if (pdfFiles.length <= 0 && csvFiles.length <= 0) {
      toast({
        title: "Invalid files",
        description: "Please drop either a CSV or PDF file only",
        variant: "destructive",
      });
      return;
    }

    setUploadingTransactions(true);
    try {
      if (pdfFiles.length > 0) {
        // Handle statement upload
        const formData = new FormData();
        formData.append("file", pdfFiles[0]);
        const response = await fetch(`/api/statement?month=${params.month}`, {
          method: "POST",
          body: formData,
        });
        if (!response.ok) throw new Error("Statement upload failed");
        await Promise.all([
          queryClient.invalidateQueries({
            queryKey: ["transactions", params.month],
          }),
          queryClient.invalidateQueries({
            queryKey: ["invoices", params.month],
          }),
        ]);
        toast({
          title: "Success",
          description: `${pdfFiles[0].name} statement uploaded successfully`,
        });
      }

      if (csvFiles.length > 0) {
        const formData = new FormData();
        formData.append("file", csvFiles[0]);

        const response = await fetch("/api/transactions", {
          method: "POST",
          body: formData,
        });

        if (!response.ok) throw new Error("Upload failed");

        // Invalidate both transactions and invoices queries to trigger a refetch
        await Promise.all([
          queryClient.invalidateQueries({
            queryKey: ["transactions", params.month],
          }),
          queryClient.invalidateQueries({
            queryKey: ["invoices", params.month],
          }),
        ]);

        toast({
          title: "Success",
          description: `${csvFiles[0].name} uploaded successfully`,
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to upload ${
          pdfFiles[0]?.name || csvFiles[0]?.name
        }`,
        variant: "destructive",
      });
    } finally {
      setUploadingTransactions(false);
    }
  };

  const handleTransactionClick = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
  };

  const handleInvoiceSelect = async (invoiceId: string | null) => {
    if (!selectedTransaction) return;
    setLinkingInvoice(true);
    try {
      const response = await fetch(
        `/api/transactions/${selectedTransaction.id}?month=${params.month}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ invoiceId }),
        }
      );

      if (!response.ok) throw new Error("Failed to update mapping");

      // Invalidate both transactions and invoices queries to refresh data
      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: ["transactions", params.month],
        }),
        queryClient.invalidateQueries({
          queryKey: ["invoices", params.month],
        }),
      ]);

      toast({
        title: "Success",
        description: invoiceId
          ? "Invoice linked successfully"
          : "Invoice link cleared",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update invoice link",
        variant: "destructive",
      });
    } finally {
      setSelectedTransaction(null);
      setLinkingInvoice(false);
    }
  };

  // Compute matched invoice IDs (excluding the one currently linked to the selected transaction)
  const matchedInvoiceIds = Object.entries(
    transactionsData.transactionToInvoice || {}
  )
    .filter(([txnId]) => {
      // Exclude the mapping for the currently selected transaction
      if (!selectedTransaction) return true;
      return txnId !== selectedTransaction.id;
    })
    .map(([_, invId]) => invId as string);

  // Compute filtered transactions for display and count
  const filteredTransactions = (transactionsData.transactions || []).filter(
    (transaction: Transaction) => {
      if (!transaction.dateCompleted) return false;
      const date = new Date(transaction.dateCompleted);
      if (isNaN(date.getTime())) return false;
      if (!allowedTransactionTypes.includes(transaction.type)) return false;
      return true;
    }
  );

  const handleAutoMatch = async (
    matches: Array<{ transactionId: string; invoiceId: string }>
  ) => {
    try {
      // Process matches sequentially
      for (const { transactionId, invoiceId } of matches) {
        const response = await fetch(
          `/api/transactions/${transactionId}?month=${params.month}`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ invoiceId }),
          }
        );

        if (!response.ok) {
          throw new Error(
            `Failed to update mapping for transaction ${transactionId}`
          );
        }
      }

      // Invalidate both transactions and invoices queries to refresh data
      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: ["transactions", params.month],
        }),
        queryClient.invalidateQueries({
          queryKey: ["invoices", params.month],
        }),
      ]);

      toast({
        title: "Success",
        description: `Successfully matched ${matches.length} transactions`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update some matches",
        variant: "destructive",
      });
    }
  };

  const handleIgnoreTransaction = async (transactionId: string) => {
    try {
      const response = await fetch(
        `/api/transactions/${transactionId}?month=${params.month}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ invoiceId: "<ignore>" }),
        }
      );

      if (!response.ok) throw new Error("Failed to ignore transaction");

      // Invalidate both transactions and invoices queries to refresh data
      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: ["transactions", params.month],
        }),
        queryClient.invalidateQueries({
          queryKey: ["invoices", params.month],
        }),
      ]);

      toast({
        title: "Success",
        description: "Transaction ignored",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to ignore transaction",
        variant: "destructive",
      });
    }
  };

  // InvoiceRow component for rendering an invoice in the list
  function InvoiceRow({
    invoice,
    isMapped,
    onDelete,
    onDownload,
    onView,
    isDeleting,
  }: {
    invoice: Invoice;
    isMapped: boolean;
    onDelete?: (id: string, name: string) => void;
    onDownload?: (id: string, name: string) => void;
    onView?: () => void;
    isDeleting?: boolean;
  }) {
    return (
      <li
        className={`bg-muted/50 rounded-lg overflow-hidden ${
          isMapped || isDeleting ? "opacity-60" : ""
        } border border-muted`}
      >
        <div className="bg-muted grid grid-cols-[auto_auto_1fr_auto_auto] items-center gap-4 p-1 border-b border-muted">
          <div
            className={`px-2 py-1 rounded text-xs font-medium h-full flex items-center justify-center ${
              invoice.hasMyVAT
                ? "bg-green-100 text-green-700"
                : "bg-red-100 text-red-700"
            } w-20`}
          >
            {invoice.hasMyVAT ? "VAT OK" : "VAT N/A"}
          </div>
          <div className="text-sm font-medium tabular-nums">
            {invoice.issueDate
              ? format(new Date(invoice.issueDate), "MMM dd")
              : "-"}
          </div>
          <div className="flex flex-col min-w-0">
            <span className="text-sm font-medium truncate">
              {invoice.issuer}
            </span>
            <span className="text-[10px] text-muted-foreground truncate">
              {invoice.name}
            </span>
          </div>
          <div className="text-sm font-medium tabular-nums">
            {invoice.totalAmount && invoice.currency
              ? formatAmount(invoice.totalAmount, invoice.currency)
              : "-"}
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={onView}
              disabled={isDeleting}
              className="h-8 w-8"
            >
              <FileText className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onDownload?.(invoice.id, invoice.name)}
              disabled={isDeleting}
              className="h-8 w-8"
            >
              <Download className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onDelete?.(invoice.id, invoice.name)}
              disabled={isDeleting}
              className="h-8 w-8"
            >
              <Trash2
                className={`h-4 w-4 ${isDeleting ? "animate-spin" : ""}`}
              />
            </Button>
          </div>
        </div>
        {invoice.items && invoice.items.length > 0 && (
          <div className="px-2 py-1 space-y-0">
            {invoice.items.map((item, index) => (
              <div
                key={index}
                className="text-[10px] text-muted-foreground truncate"
              >
                • {item}
              </div>
            ))}
          </div>
        )}
      </li>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-5xl min-h-screen">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-2">
          <h1 className="text-3xl font-bold">
            Invoices: {format(currentMonth, "MMMM yyyy")}
          </h1>
          {isLoading && (
            <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" onClick={handlePrevMonth}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={handleNextMonth}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {emailText && (
        <pre className="whitespace-pre-wrap bg-white p-6 rounded-lg mb-8 border">
          {emailText}
        </pre>
      )}

      <DropZone
        isDragging={isDraggingTransactions}
        onDragOver={handleTransactionsDragOver}
        onDragLeave={handleTransactionsDragLeave}
        onDrop={handleTransactionsDrop}
        className="mb-8"
      >
        <Card className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h2 className="text-xl font-semibold">Transactions</h2>
              <div className="text-xs text-muted-foreground mt-1">
                {filteredTransactions.length} transactions
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="opacity-30 hover:opacity-100"
                onClick={async () => {
                  try {
                    const response = await fetch(
                      `/api/revolut?month=${params.month}`
                    );
                    if (!response.ok)
                      throw new Error("Failed to fetch Revolut transactions");

                    await Promise.all([
                      queryClient.invalidateQueries({
                        queryKey: ["transactions", params.month],
                      }),
                      queryClient.invalidateQueries({
                        queryKey: ["invoices", params.month],
                      }),
                    ]);

                    toast({
                      title: "Success",
                      description:
                        "Transactions fetched from Revolut successfully",
                    });
                  } catch (error) {
                    console.error(
                      "❌ Error fetching Revolut transactions:",
                      error
                    );
                    toast({
                      title: "Error",
                      description: "Failed to fetch transactions from Revolut",
                      variant: "destructive",
                    });
                  }
                }}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
              {transactionsData.revolutStatementUrl ? (
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-green-100 text-green-700 hover:bg-green-200 hover:text-green-800"
                  onClick={() => {
                    fetch(`/api/statement?month=${params.month}`)
                      .then((res) => res.blob())
                      .then((blob) => {
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement("a");
                        a.href = url;
                        a.download = `revolut-statement-${params.month}.pdf`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                      })
                      .catch((err) => {
                        console.error("❌ Error downloading statement:", err);
                        toast({
                          title: "Error",
                          description: "Failed to download statement",
                          variant: "destructive",
                        });
                      });
                  }}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Statement PDF
                </Button>
              ) : (
                <span className="px-3 py-1 rounded-full bg-orange-100 text-orange-700 text-xs font-semibold">
                  No statement PDF
                </span>
              )}
            </div>
          </div>
          <TransactionsList
            transactions={filteredTransactions}
            isLoading={isLoadingTransactions}
            isUploading={uploadingTransactions}
            onTransactionClick={handleTransactionClick}
            onClearTransactions={async () => {
              try {
                const response = await fetch(
                  `/api/transactions?month=${params.month}`,
                  {
                    method: "DELETE",
                  }
                );
                if (!response.ok)
                  throw new Error("Failed to clear transactions");
                await queryClient.invalidateQueries({
                  queryKey: ["transactions", params.month],
                });
                toast({
                  title: "Success",
                  description: "Transactions cleared successfully",
                });
              } catch (error) {
                toast({
                  title: "Error",
                  description: "Failed to clear transactions",
                  variant: "destructive",
                });
              }
            }}
            transactionToInvoice={transactionsData.transactionToInvoice}
            invoices={invoices}
            onAutoMatch={handleAutoMatch}
            onIgnoreTransaction={handleIgnoreTransaction}
          />
        </Card>
      </DropZone>

      <DropZone
        isDragging={isDragging}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <Card className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h2 className="text-xl font-semibold">Uploaded Invoices</h2>
              <div className="text-xs text-muted-foreground mt-1">
                {invoices.length} invoices
              </div>
            </div>
            <div className="flex items-center gap-2">
              {invoices.length > 0 && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      downloadAllInvoicesToFolder(invoices, params.month)
                    }
                    disabled={isLoading || uploadingFiles.size > 0}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download All
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="destructive"
                        size="sm"
                        disabled={
                          isDeletingAll || uploadingFiles.size > 0 || isLoading
                        }
                      >
                        {isDeletingAll ? (
                          <>
                            <Trash2 className="h-4 w-4 mr-2 animate-spin" />
                            Deleting...
                          </>
                        ) : (
                          <>
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete All
                          </>
                        )}
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>
                          Are you absolutely sure?
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                          This action cannot be undone. This will permanently
                          delete all uploaded invoices.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDeleteAll}>
                          Delete All
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </>
              )}
            </div>
          </div>
          {isLoading ? (
            <div className="text-center py-12 text-muted-foreground">
              <Loader2 className="mx-auto h-12 w-12 mb-4 animate-spin" />
              <p className="text-lg font-medium mb-2">Loading invoices...</p>
            </div>
          ) : invoices.length === 0 && uploadingFiles.size === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <Upload className="mx-auto h-12 w-12 mb-4" />
              <p className="text-lg font-medium mb-2">Drop PDF Invoices Here</p>
              <p className="text-sm">
                Drag and drop your PDF invoices to upload
              </p>
            </div>
          ) : (
            <>
              <ul className="space-y-2">
                {(() => {
                  // Show loading skeletons for files being uploaded
                  const uploadingSkeletons = Array.from(
                    uploadingFiles.entries()
                  ).map(([name]) => (
                    <li
                      key={name}
                      className="bg-muted/50 rounded-lg overflow-hidden border border-muted animate-pulse"
                    >
                      <div className="bg-muted grid grid-cols-[auto_auto_1fr_auto_auto] items-center gap-4 p-1 border-b border-muted">
                        <div className="px-2 py-1 rounded text-xs font-medium h-full flex items-center justify-center w-20 bg-muted-foreground/20" />
                        <div className="text-sm font-medium tabular-nums bg-muted-foreground/20 w-16 h-4 rounded" />
                        <div className="flex flex-col min-w-0">
                          <span className="text-sm font-medium truncate bg-muted-foreground/20 w-32 h-4 rounded mb-1" />
                          <span className="text-[10px] text-muted-foreground truncate bg-muted-foreground/20 w-24 h-3 rounded" />
                        </div>
                        <div className="text-sm font-medium tabular-nums bg-muted-foreground/20 w-20 h-4 rounded" />
                        <div className="flex gap-1">
                          <div className="h-8 w-8 rounded bg-muted-foreground/20" />
                          <div className="h-8 w-8 rounded bg-muted-foreground/20" />
                          <div className="h-8 w-8 rounded bg-muted-foreground/20" />
                        </div>
                      </div>
                    </li>
                  ));

                  // Find mapped and unmapped invoices
                  const mappedInvoiceIds = Object.values(
                    transactionsData.transactionToInvoice || {}
                  );
                  const unmapped = invoices
                    .filter((inv) => !mappedInvoiceIds.includes(inv.id))
                    .toReversed();
                  const mapped = invoices
                    .filter((inv) => mappedInvoiceIds.includes(inv.id))
                    .toReversed();

                  return (
                    <>
                      {uploadingSkeletons}
                      {unmapped.map((invoice) => (
                        <InvoiceRow
                          key={invoice.id}
                          invoice={invoice}
                          isMapped={false}
                          onDelete={handleDelete}
                          onDownload={handleDownload}
                          onView={() => setSelectedInvoice(invoice)}
                          isDeleting={deletingFiles.has(invoice.id)}
                        />
                      ))}
                      {mapped.length > 0 && (
                        <Fragment>
                          <li className="flex items-center gap-2 py-2 opacity-70">
                            <hr className="flex-grow border-t border-muted-foreground/20" />
                            <span className="text-xs text-muted-foreground font-medium px-2">
                              Mapped Invoices
                            </span>
                            <hr className="flex-grow border-t border-muted-foreground/20" />
                          </li>
                          {mapped.map((invoice) => (
                            <InvoiceRow
                              key={invoice.id}
                              invoice={invoice}
                              isMapped={true}
                              onDelete={handleDelete}
                              onDownload={handleDownload}
                              onView={() => setSelectedInvoice(invoice)}
                              isDeleting={deletingFiles.has(invoice.id)}
                            />
                          ))}
                        </Fragment>
                      )}
                    </>
                  );
                })()}
              </ul>
              {/* Totals footer for invoices */}
              {(() => {
                // Calculate totals by currency for all invoices
                const totals: Record<string, number> = {};
                invoices.forEach((inv) => {
                  if (inv.totalAmount !== null && inv.currency) {
                    totals[inv.currency] =
                      (totals[inv.currency] || 0) + inv.totalAmount;
                  }
                });

                if (Object.keys(totals).length === 0) return null;

                return (
                  <div className="flex gap-4 justify-end text-xs text-muted-foreground mt-2">
                    {Object.entries(totals).map(([currency, total]) => (
                      <span key={currency} className="font-mono">
                        Total {currency}:{" "}
                        <span className="text-foreground">
                          {formatAmount(total, currency)}
                        </span>
                      </span>
                    ))}
                  </div>
                );
              })()}
            </>
          )}
        </Card>
      </DropZone>

      <InvoiceTextModal
        isOpen={!!selectedInvoice}
        onClose={() => setSelectedInvoice(undefined)}
        invoice={selectedInvoice}
      />

      {linkingInvoice ? (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
        </div>
      ) : (
        <InvoiceSelectDialog
          isOpen={!!selectedTransaction}
          onClose={() => setSelectedTransaction(null)}
          onSelect={handleInvoiceSelect}
          invoices={invoices}
          selectedInvoiceId={
            selectedTransaction
              ? transactionsData.transactionToInvoice?.[selectedTransaction.id]
              : undefined
          }
          transaction={selectedTransaction}
          matchedInvoiceIds={matchedInvoiceIds}
        />
      )}
    </div>
  );
}
