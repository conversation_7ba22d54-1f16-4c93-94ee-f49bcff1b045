import { NextResponse } from 'next/server';
import { getDb } from '@/lib/blob-storage';
import { parse } from 'date-fns';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  // Get the month from the URL search params
  const url = new URL(request.url);
  const monthParam = url.searchParams.get("month");
  
  console.log("📥 Attempting to download invoice:", params.id, "for month:", monthParam);

  const targetDate = monthParam
    ? parse(monthParam, "yyyy-MM", new Date())
    : new Date();

  const db = await getDb(targetDate);
  const invoice = db.data.invoices?.find(inv => inv.id === params.id);
  
  if (!invoice) {
    console.log("❌ Invoice not found in database");
    return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
  }

  // Fetch PDF from blob storage
  try {
    const response = await fetch(invoice.pdfUrl);
    if (!response.ok) {
      console.log("❌ Failed to fetch PDF from blob storage");
      return NextResponse.json({ error: 'PDF not found in storage' }, { status: 404 });
    }

    const content = await response.arrayBuffer();

    console.log("✅ Invoice found, sending file");
    return new NextResponse(content, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${invoice.name}"`,
      },
    });
  } catch (error) {
    console.error("❌ Error fetching PDF from blob storage:", error);
    return NextResponse.json({ error: 'Failed to fetch PDF' }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Get the month from the URL search params
    const url = new URL(request.url);
    const monthParam = url.searchParams.get("month");
    
    console.log("🗑️ Attempting to delete invoice:", params.id, "for month:", monthParam);

    const targetDate = monthParam
      ? parse(monthParam, "yyyy-MM", new Date())
      : new Date();

    const db = await getDb(targetDate);
    const index = db.data.invoices?.findIndex(inv => inv.id === params.id) ?? -1;
    
    if (index === -1) {
      console.log("❌ Invoice not found in database");
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Remove any transactionToInvoice mappings that point to this invoice
    if (db.data.transactionToInvoice) {
      for (const [txnId, invId] of Object.entries(db.data.transactionToInvoice)) {
        if (invId === params.id) {
          delete db.data.transactionToInvoice[txnId];
        }
      }
    }

    db.data.invoices?.splice(index, 1);
    await db.write();
    
    console.log("✅ Invoice deleted successfully");
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("💥 Delete error:", error);
    return NextResponse.json({ error: 'Failed to delete invoice' }, { status: 500 });
  }
}
