import { NextRequest, NextResponse } from "next/server";
import { getDb, storeStatementPdf, getStatementPdf } from "@/lib/blob-storage";
import { parse, format } from "date-fns";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const month = request.nextUrl.searchParams.get("month");

    if (!file || !file.type.includes("pdf")) {
      return NextResponse.json({ error: "No PDF file provided" }, { status: 400 });
    }

    const buffer = Buffer.from(await file.arrayBuffer());
    const targetDate = month ? parse(month, "yyyy-MM", new Date()) : new Date();
    const monthStr = format(targetDate, 'yyyy-MM');

    // Store PDF in blob storage
    const pdfUrl = await storeStatementPdf(monthStr, buffer);

    // Store the blob URL in database
    const { data, write } = await getDb(targetDate);
    data.revolutStatementUrl = pdfUrl;
    await write();

    return NextResponse.json({ success: true, pdfUrl });
  } catch (error) {
    console.error("❌ Error uploading statement:", error);
    return NextResponse.json({ error: "Failed to upload statement" }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const month = request.nextUrl.searchParams.get("month");
    if (!month) {
      return NextResponse.json({ error: "Month parameter is required" }, { status: 400 });
    }

    // Try to get PDF from blob storage first
    const pdfBuffer = await getStatementPdf(month);

    if (pdfBuffer) {
      // Return the PDF from blob storage
      return new NextResponse(new Uint8Array(pdfBuffer), {
        headers: {
          "Content-Type": "application/pdf",
          "Content-Disposition": `attachment; filename="revolut-statement-${month}.pdf"`,
        },
      });
    }

    // Get statement URL from database
    const targetDate = parse(month, "yyyy-MM", new Date());
    const { data } = await getDb(targetDate);

    if (!data.revolutStatementUrl) {
      return NextResponse.json({ error: "Statement not found" }, { status: 404 });
    }

    // Fetch PDF from blob storage
    const response = await fetch(data.revolutStatementUrl);
    if (!response.ok) {
      return NextResponse.json({ error: "Statement not found in storage" }, { status: 404 });
    }

    const buffer = await response.arrayBuffer();

    // Return the PDF with appropriate headers
    return new NextResponse(buffer, {
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="revolut-statement-${month}.pdf"`,
      },
    });
  } catch (error) {
    console.error("❌ Error downloading statement:", error);
    return NextResponse.json({ error: "Failed to download statement" }, { status: 500 });
  }
}
