import { NextRequest, NextResponse } from "next/server";
import { getDb, storeCsvFile } from "@/lib/blob-storage";
import { parse } from "date-fns";

// Helper function to safely parse a float
const safeParseFloat = (value: string): number | null => {
  if (value === null || value === undefined || value.trim() === "") {
    return null;
  }
  const num = parseFloat(value);
  return isNaN(num) ? null : num;
};

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const month = searchParams.get("month");

  if (!month) {
    return NextResponse.json(
      { error: "Month parameter is required" },
      { status: 400 }
    );
  }

  try {
    const targetDate = parse(month, "yyyy-MM", new Date());
    const { data } = await getDb(targetDate);
    return NextResponse.json({
      transactions: data.transactions,
      transactionToInvoice: data.transactionToInvoice,
      revolutStatementUrl: data.revolutStatementUrl ?? null,
    });
  } catch (error) {
    console.error("❌ Error fetching transactions:", error);
    return NextResponse.json(
      { error: "Failed to fetch transactions" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    const text = await file.text();

    const lines = text
      .split("\n")
      .map((line) => line.trim())
      .filter(Boolean)
      .filter((line) => /^\d{4}-\d{2}-\d{2}/.test(line)); // Only keep lines that start with a date

    console.log("📊 Found", lines.length, "transactions");

    if (lines.length === 0) {
      return NextResponse.json(
        { error: "No valid transaction records found in the CSV." },
        { status: 400 }
      );
    }

    // Get the month from the first line's date
    const firstDate = new Date(lines[0].split(",")[1]); // Date completed is second column

    // Store CSV file in blob storage with correct month
    const csvBuffer = Buffer.from(text);
    const monthStr = firstDate.toISOString().slice(0, 7); // YYYY-MM format
    await storeCsvFile(file.name, monthStr, csvBuffer);
    const { data, write } = await getDb(firstDate);

    // Parse all lines as transactions
    const transactions = lines.map((line) => {
      const [
        dateStarted, ///////////////////// Date/started/(UTC)
        dateCompleted, /////////////////// Date/completed/(UTC)
        id, ////////////////////////////// ID
        type, //////////////////////////// Type
        state, /////////////////////////// State
        description, ///////////////////// Description
        reference, /////////////////////// Reference
        payer, /////////////////////////// Payer
        cardNumber, ////////////////////// Card/number
        cardLabel, /////////////////////// Card/label
        cardState, /////////////////////// Card/state
        origCurrency, //////////////////// Orig/currency
        origAmount, ////////////////////// Orig/amount
        paymentCurrency, ///////////////// Payment/currency
        amount, ////////////////////////// Amount
        totalAmount, ///////////////////// Total/amount
        exchangeRate, //////////////////// Exchange/rate
        fee, ///////////////////////////// Fee
        feeCurrency, ///////////////////// Fee/currency
        balance, ///////////////////////// Balance
        account, ///////////////////////// Account
        beneficiaryAccountNumber, //////// Beneficiary/account/number
        beneficiarySortCode, ///////////// Beneficiary/sort/code/or/routing/number
        beneficiaryIBAN, ///////////////// Beneficiary/IBAN
        beneficiaryBIC, ////////////////// Beneficiary/BIC
        mcc, ///////////////////////////// MCC
        relatedTransactionId, //////////// Related/transaction/id
        spendProgram, //////////////////// Spend/program
      ] = line.split(",");

      return {
        dateStarted: dateStarted || null,
        dateCompleted: dateCompleted || null,
        id: id || "",
        type: type || "",
        state: state || "",
        description: description || "",
        reference: reference || "",
        payer: payer || "",
        cardNumber: cardNumber || "",
        cardLabel: cardLabel || "",
        cardState: cardState || "",
        origCurrency: origCurrency || "",
        origAmount: safeParseFloat(origAmount),
        paymentCurrency: paymentCurrency || "",
        amount: safeParseFloat(amount),
        totalAmount: safeParseFloat(totalAmount),
        exchangeRate: safeParseFloat(exchangeRate),
        fee: safeParseFloat(fee),
        feeCurrency: feeCurrency || "",
        balance: safeParseFloat(balance),
        account: account || "",
        beneficiaryAccountNumber: beneficiaryAccountNumber || "",
        beneficiarySortCode: beneficiarySortCode || "",
        beneficiaryIBAN: beneficiaryIBAN || "",
        beneficiaryBIC: beneficiaryBIC || "",
        mcc: mcc || "",
        relatedTransactionId: relatedTransactionId || "",
        spendProgram: spendProgram || "",
      };
    });

    data.transactions = transactions;
    await write();

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("❌ Error uploading transactions:", error);
    return NextResponse.json(
      { error: "Failed to upload transactions" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const month = searchParams.get("month");

  if (!month) {
    return NextResponse.json(
      { error: "Month parameter is required" },
      { status: 400 }
    );
  }

  try {
    const targetDate = parse(month, "yyyy-MM", new Date());
    const { data, write } = await getDb(targetDate);
    data.transactions = [];
    data.transactionToInvoice = {};
    await write();
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("❌ Error clearing transactions:", error);
    return NextResponse.json(
      { error: "Failed to clear transactions" },
      { status: 500 }
    );
  }
}
