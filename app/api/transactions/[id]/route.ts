import { NextRequest, NextResponse } from "next/server";
import { getDb } from "@/lib/blob-storage";
import { parse } from "date-fns";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { invoiceId } = await request.json();
    const month = request.nextUrl.searchParams.get("month");

    if (!month) {
      return NextResponse.json(
        { error: "Month parameter is required" },
        { status: 400 }
      );
    }

    const targetDate = parse(month, "yyyy-MM", new Date());
    const { data, write } = await getDb(targetDate);

    // Ensure mapping object exists
    if (!data.transactionToInvoice) {
      data.transactionToInvoice = {};
    }

    // Update the mapping
    if (invoiceId) {
      data.transactionToInvoice[params.id] = invoiceId;
    } else {
      delete data.transactionToInvoice[params.id];
    }

    await write();

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("❌ Error updating transaction mapping:", error);
    return NextResponse.json(
      { error: "Failed to update transaction mapping" },
      { status: 500 }
    );
  }
} 
