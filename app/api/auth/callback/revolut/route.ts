import { NextRequest, NextResponse } from "next/server";
import https from "https";
import qs from "querystring";

const CLIENT_ID = process.env.REVOLUT_CLIENT_ID!;
const REDIRECT_URI = "https://revoltage-acc-emailer.vercel.app/api/auth/callback/revolut";
const TOKEN_URL = "https://oba-auth.revolut.com/token";

export async function GET(request: NextRequest) {
  const CERT_B64 = process.env.REVOLUT_CERT_B64;
  const KEY_B64 = process.env.REVOLUT_KEY_B64;

  if (!CERT_B64 || !KEY_B64) {
    return NextResponse.json({ error: "REVOLUT_CERT_B64 and/or REVOLUT_KEY_B64 environment variables are not set." }, { status: 500 });
  }

  const CERT = Buffer.from(CERT_B64, "base64").toString("utf-8");
  const KEY = Buffer.from(KEY_B64, "base64").toString("utf-8");

  const { searchParams } = new URL(request.url);
  const code = searchParams.get("code");

  if (!code) {
    return NextResponse.json({ error: "Missing code" }, { status: 400 });
  }

  const postData = qs.stringify({
    grant_type: "authorization_code",
    code,
    redirect_uri: REDIRECT_URI,
    client_id: CLIENT_ID,
  });

  const agent = new https.Agent({ cert: CERT, key: KEY });

  const response = await fetch(TOKEN_URL, {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: postData,
    // @ts-ignore
    agent,
  });

  if (!response.ok) {
    const error = await response.text();
    return NextResponse.json({ error }, { status: 500 });
  }

  const tokenData = await response.json();
  return NextResponse.json(tokenData);
}