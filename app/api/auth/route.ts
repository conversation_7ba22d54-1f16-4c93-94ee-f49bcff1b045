import { cookies } from "next/headers";
import { NextResponse } from "next/server";

// This should be stored in an environment variable in production
const PASSWORD = process.env.SITE_PASSWORD || "z";

export async function POST(request: Request) {
  const { password } = await request.json();

  if (password === PASSWORD) {
    // Set a cookie that expires in 24 hours
    cookies().set("auth", "true", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 60 * 60 * 24, // 24 hours
    });

    return new NextResponse(null, { status: 200 });
  }

  return new NextResponse(null, { status: 401 });
} 