const currencyLocales: Record<string, string> = {
  USD: "en-US",
  EUR: "de-DE",
  BGN: "bg-BG",
  GBP: "en-GB",
};

// Map non-standard currency codes to standard ones
const currencyCodeMap: Record<string, string> = {
  "лв.": "BGN",
  "лв": "BGN",
  "€": "EUR",
  "$": "USD",
  "£": "GBP",
};

export function formatCurrency(amount: number | null, currency: string | null): string | null {
  if (amount === null || currency === null) return null;

  try {
    const standardCurrency = currencyCodeMap[currency] || currency;
    const locale = currencyLocales[standardCurrency] || "en-US";

    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency: standardCurrency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  } catch (error) {
    console.error("💥 Error formatting currency:", { amount, currency, error });
    // Fallback to a simple format if Intl.NumberFormat fails
    return `${amount.toFixed(2)} ${currency}`;
  }
} 