import { Invoice } from '@/types/invoice';
import { format } from 'date-fns';
import { bg } from 'date-fns/locale';

export function createEmailMessage(invoices: Invoice[], hasRevolutStatement: boolean = false) {
  if (invoices.length === 0) {
    return null;
  }

  // Get the month from the first invoice's date, or use current date
  const firstInvoiceDate = invoices[0]?.issueDate 
    ? new Date(invoices[0].issueDate) 
    : new Date();

  const monthName = format(firstInvoiceDate, 'LLLL', { locale: bg });
  const year = format(firstInvoiceDate, 'yyyy');

  const hasInvoicesWithMissingVat = invoices.some(inv => !inv.hasMyVAT);

  return (
    `Здравей,\n\n` +
    `Изпращам ти ${hasRevolutStatement ? 'извлеченията от Revolut и ' : ''}фактурите за месец ${monthName} ${year}:\n\n` +
    `${invoices
      .map(inv => `• ${inv.issuer}${!inv.hasMyVAT ? ' (Липсва ДДС)' : ''}`)
      .join('\n')}` +
    (hasInvoicesWithMissingVat
      ? '\n\n⚠️ Някои фактури са без ДДС номер и трябва да бъдат коригирани!'
      : '')
  );
}
